<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vijino World - Coming Soon</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #000;
            color: #fff;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            background-image: radial-gradient(circle at center, #1a1a1a 0%, #000 100%);
            overflow: hidden;
            animation: fadeIn 2s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInFromTop {
            from {
                transform: translateY(-100px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideInFromBottom {
            from {
                transform: translateY(100px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes glow {
            0% { text-shadow: 0 0 5px rgba(110, 69, 226, 0.5); }
            50% { text-shadow: 0 0 20px rgba(110, 69, 226, 0.8), 0 0 30px rgba(136, 211, 206, 0.6); }
            100% { text-shadow: 0 0 5px rgba(110, 69, 226, 0.5); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes countdownPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            letter-spacing: 5px;
            text-transform: uppercase;
            color: #fff;
            animation: slideInFromTop 1.5s ease-out, glow 3s ease-in-out infinite;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.1);
            text-shadow: 0 0 30px rgba(110, 69, 226, 1), 0 0 40px rgba(136, 211, 206, 0.8);
        }

        .tagline {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 3rem;
            letter-spacing: 2px;
            max-width: 800px;
            line-height: 1.5;
            animation: slideInFromBottom 1.5s ease-out 0.5s both;
            transition: all 0.3s ease;
        }

        .tagline:hover {
            color: #88d3ce;
            transform: translateY(-5px);
        }
        
        .countdown-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 3rem;
            animation: slideInFromBottom 1.5s ease-out 1s both;
        }

        .countdown-title {
            font-size: 1.2rem;
            font-weight: 400;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #ccc;
            animation: float 3s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .countdown-title:hover {
            color: #6e45e2;
            transform: scale(1.1);
        }

        .countdown {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            animation: slideInFromBottom 1.5s ease-out calc(1.2s + var(--delay, 0s)) both;
        }

        .countdown-item:nth-child(1) { --delay: 0s; }
        .countdown-item:nth-child(2) { --delay: 0.1s; }
        .countdown-item:nth-child(3) { --delay: 0.2s; }
        .countdown-item:nth-child(4) { --delay: 0.3s; }

        .countdown-item:hover {
            transform: translateY(-10px) scale(1.05);
            background: rgba(110, 69, 226, 0.2);
            border-color: rgba(110, 69, 226, 0.5);
            box-shadow: 0 20px 40px rgba(110, 69, 226, 0.3);
        }

        .countdown-value {
            font-size: 4rem;
            font-weight: 600;
            background: linear-gradient(135deg, #6e45e2, #88d3ce);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            line-height: 1;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .countdown-value.pulse {
            animation: countdownPulse 0.6s ease-in-out;
        }

        .countdown-label {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #aaa;
            transition: all 0.3s ease;
        }

        .countdown-item:hover .countdown-label {
            color: #88d3ce;
        }
        
        .subscribe {
            margin-top: 2rem;
            animation: slideInFromBottom 1.5s ease-out 1.5s both;
        }

        .subscribe-btn {
            padding: 1rem 3rem;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #fff;
            background: linear-gradient(135deg, #6e45e2, #88d3ce);
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .subscribe-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .subscribe-btn:hover::before {
            left: 100%;
        }

        .subscribe-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 20px 40px rgba(110, 69, 226, 0.4);
            background: linear-gradient(135deg, #7c52ff, #9ae6e1);
        }

        .subscribe-btn:active {
            transform: translateY(-2px) scale(1.02);
            transition: all 0.1s ease;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* Loading animation for countdown */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(110, 69, 226, 0.3);
            border-radius: 50%;
            border-top-color: #6e45e2;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #6e45e2, #88d3ce);
            color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        /* Footer Styles */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 2rem;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            z-index: 100;
            animation: slideInFromBottom 2s ease-out 2s both;
        }

        .footer-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .footer-link {
            color: #aaa;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 300;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }

        .footer-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, #6e45e2, #88d3ce);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .footer-link:hover {
            color: #fff;
            background: rgba(110, 69, 226, 0.1);
            transform: translateY(-2px);
        }

        .footer-link:hover::before {
            width: 80%;
        }

        .footer-separator {
            width: 1px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
        }

        .footer-copyright {
            color: #666;
            font-size: 0.8rem;
            font-weight: 300;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            margin: 5% auto;
            padding: 2rem;
            border: 1px solid rgba(110, 69, 226, 0.3);
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            animation: slideInFromTop 0.3s ease;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #fff;
            background: linear-gradient(135deg, #6e45e2, #88d3ce);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .close {
            color: #aaa;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close:hover {
            color: #fff;
            background: rgba(110, 69, 226, 0.2);
            transform: rotate(90deg);
        }

        .modal-body {
            color: #ccc;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .modal-body h3 {
            color: #fff;
            margin: 1.5rem 0 1rem 0;
            font-size: 1.2rem;
        }

        .modal-body p {
            margin-bottom: 1rem;
        }

        .modal-body ul {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .modal-body li {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .logo {
                font-size: 2rem;
            }

            .tagline {
                font-size: 1rem;
                padding: 0 1rem;
            }

            .countdown {
                gap: 1rem;
            }

            .countdown-value {
                font-size: 2.5rem;
            }

            .countdown-label {
                font-size: 0.7rem;
            }

            .subscribe-btn {
                padding: 0.8rem 2rem;
                font-size: 0.9rem;
            }

            .footer {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .footer-links {
                gap: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            .footer-separator {
                display: none;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 1.5rem;
            }

            .modal-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles-js"></div>
    
    <div class="logo">Vijino World</div>
    <div class="tagline">Time to Celebrate a Wonderful New Experience</div>
    
    <div class="countdown-container">
        <div class="countdown-title">Coming Soon</div>
        <div class="countdown" id="countdown">
            <div class="countdown-item">
                <div class="countdown-value" id="days">00</div>
                <div class="countdown-label">days</div>
            </div>
            <div class="countdown-item">
                <div class="countdown-value" id="hours">00</div>
                <div class="countdown-label">hours</div>
            </div>
            <div class="countdown-item">
                <div class="countdown-value" id="minutes">00</div>
                <div class="countdown-label">minutes</div>
            </div>
            <div class="countdown-item">
                <div class="countdown-value" id="seconds">00</div>
                <div class="countdown-label">seconds</div>
            </div>
        </div>
    </div>
    
    <div class="subscribe">
        <button class="subscribe-btn">SUBSCRIBE</button>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-links">
            <a href="#" class="footer-link" onclick="openModal('termsModal')">Terms & Conditions</a>
            <div class="footer-separator"></div>
            <a href="#" class="footer-link" onclick="openModal('privacyModal')">Privacy Policy</a>
            <div class="footer-separator"></div>
            <span class="footer-copyright">© 2024 Vijino World. All rights reserved.</span>
        </div>
    </footer>

    <!-- Terms & Conditions Modal -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Terms & Conditions</h2>
                <span class="close" onclick="closeModal('termsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <p><strong>Last updated:</strong> January 2024</p>

                <h3>1. Acceptance of Terms</h3>
                <p>By accessing and using Vijino World, you accept and agree to be bound by the terms and provision of this agreement.</p>

                <h3>2. Use License</h3>
                <p>Permission is granted to temporarily download one copy of Vijino World materials for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:</p>
                <ul>
                    <li>modify or copy the materials</li>
                    <li>use the materials for any commercial purpose or for any public display</li>
                    <li>attempt to reverse engineer any software contained on the website</li>
                    <li>remove any copyright or other proprietary notations from the materials</li>
                </ul>

                <h3>3. Disclaimer</h3>
                <p>The materials on Vijino World are provided on an 'as is' basis. Vijino World makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>

                <h3>4. Limitations</h3>
                <p>In no event shall Vijino World or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on Vijino World, even if Vijino World or an authorized representative has been notified orally or in writing of the possibility of such damage.</p>

                <h3>5. Contact Information</h3>
                <p>If you have any questions about these Terms & Conditions, please contact <NAME_EMAIL></p>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Privacy Policy</h2>
                <span class="close" onclick="closeModal('privacyModal')">&times;</span>
            </div>
            <div class="modal-body">
                <p><strong>Last updated:</strong> January 2024</p>

                <h3>1. Information We Collect</h3>
                <p>We collect information you provide directly to us, such as when you subscribe to our newsletter or contact us. This may include:</p>
                <ul>
                    <li>Email addresses</li>
                    <li>Name and contact information</li>
                    <li>Any other information you choose to provide</li>
                </ul>

                <h3>2. How We Use Your Information</h3>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Send you updates about Vijino World</li>
                    <li>Respond to your comments and questions</li>
                    <li>Improve our services</li>
                    <li>Comply with legal obligations</li>
                </ul>

                <h3>3. Information Sharing</h3>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy or as required by law.</p>

                <h3>4. Data Security</h3>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

                <h3>5. Your Rights</h3>
                <p>You have the right to:</p>
                <ul>
                    <li>Access your personal information</li>
                    <li>Correct inaccurate information</li>
                    <li>Request deletion of your information</li>
                    <li>Unsubscribe from our communications</li>
                </ul>

                <h3>6. Contact Us</h3>
                <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script>
        // Set the date we're counting down to (30 days from now)
        const countDownDate = new Date();
        countDownDate.setDate(countDownDate.getDate() + 30);

        let previousValues = { days: null, hours: null, minutes: null, seconds: null };

        // Function to add pulse animation when value changes
        function addPulseAnimation(elementId) {
            const element = document.getElementById(elementId);
            element.classList.add('pulse');
            setTimeout(() => {
                element.classList.remove('pulse');
            }, 600);
        }

        // Function to show notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Update the countdown every 1 second
        const x = setInterval(function() {
            // Get today's date and time
            const now = new Date().getTime();

            // Find the distance between now and the countdown date
            const distance = countDownDate - now;

            // Time calculations for days, hours, minutes and seconds
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            // Check for changes and add pulse animation
            if (previousValues.days !== null && previousValues.days !== days) {
                addPulseAnimation('days');
            }
            if (previousValues.hours !== null && previousValues.hours !== hours) {
                addPulseAnimation('hours');
            }
            if (previousValues.minutes !== null && previousValues.minutes !== minutes) {
                addPulseAnimation('minutes');
            }
            if (previousValues.seconds !== null && previousValues.seconds !== seconds) {
                addPulseAnimation('seconds');
            }

            // Update previous values
            previousValues = { days, hours, minutes, seconds };

            // Display the result with smooth transitions
            document.getElementById("days").innerHTML = days.toString().padStart(2, '0');
            document.getElementById("hours").innerHTML = hours.toString().padStart(2, '0');
            document.getElementById("minutes").innerHTML = minutes.toString().padStart(2, '0');
            document.getElementById("seconds").innerHTML = seconds.toString().padStart(2, '0');

            // If the countdown is finished, write some text
            if (distance < 0) {
                clearInterval(x);
                document.getElementById("countdown").innerHTML = "<div style='font-size: 3rem; color: #6e45e2; animation: pulse 1s infinite;'>WE'RE LIVE!</div>";
                showNotification("🎉 Vijino World is now live!");
            }
        }, 1000);
        
        // Initialize particles.js
        particlesJS("particles-js", {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#6e45e2"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#6e45e2",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 2,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 140,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
        
        // Enhanced subscribe button functionality
        document.querySelector('.subscribe-btn').addEventListener('click', function() {
            const button = this;
            const originalText = button.textContent;

            // Add loading state
            button.innerHTML = '<span class="loading"></span> SUBSCRIBING...';
            button.disabled = true;

            setTimeout(() => {
                const email = prompt('Enter your email to subscribe for updates:');
                if (email && email.includes('@')) {
                    button.innerHTML = '✓ SUBSCRIBED!';
                    button.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                    showNotification('🎉 Thank you for subscribing! We\'ll keep you updated about Vijino World.');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = 'linear-gradient(135deg, #6e45e2, #88d3ce)';
                        button.disabled = false;
                    }, 3000);
                } else if (email) {
                    button.innerHTML = '❌ INVALID EMAIL';
                    button.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                    showNotification('❌ Please enter a valid email address.');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = 'linear-gradient(135deg, #6e45e2, #88d3ce)';
                        button.disabled = false;
                    }, 2000);
                } else {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }, 1000);
        });

        // Add click effects to logo
        document.querySelector('.logo').addEventListener('click', function() {
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'slideInFromTop 1.5s ease-out, glow 3s ease-in-out infinite';
            }, 10);
            showNotification('✨ Welcome to Vijino World!');
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                document.querySelector('.subscribe-btn').click();
            }
        });

        // Add mouse trail effect
        let mouseTrail = [];
        document.addEventListener('mousemove', function(e) {
            mouseTrail.push({x: e.clientX, y: e.clientY, time: Date.now()});

            // Remove old trail points
            mouseTrail = mouseTrail.filter(point => Date.now() - point.time < 1000);

            // Create trail effect
            if (mouseTrail.length > 1) {
                const trail = document.createElement('div');
                trail.style.position = 'fixed';
                trail.style.left = e.clientX + 'px';
                trail.style.top = e.clientY + 'px';
                trail.style.width = '4px';
                trail.style.height = '4px';
                trail.style.background = 'radial-gradient(circle, rgba(110, 69, 226, 0.8), transparent)';
                trail.style.borderRadius = '50%';
                trail.style.pointerEvents = 'none';
                trail.style.zIndex = '999';
                trail.style.animation = 'fadeOut 1s ease-out forwards';
                document.body.appendChild(trail);

                setTimeout(() => {
                    if (trail.parentNode) {
                        trail.parentNode.removeChild(trail);
                    }
                }, 1000);
            }
        });

        // Add fadeOut animation for mouse trail
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; transform: scale(1); }
                to { opacity: 0; transform: scale(0); }
            }
        `;
        document.head.appendChild(style);

        // Modal functionality
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';

            // Add click outside to close
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal(modalId);
                }
            });
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
            }
        });

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>