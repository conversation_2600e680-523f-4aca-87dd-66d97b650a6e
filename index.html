<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vijino World - Coming Soon</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            /* background: url('pexels-pixabay-531880.jpg') center/cover no-repeat fixed; */
            color: #2c2420;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            overflow: hidden;
            animation: fadeIn 2s ease-in-out;
            position: relative;
            cursor: none;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(245, 243, 240, 0.25) 50%, rgba(255, 255, 255, 0.1) 100%),
                radial-gradient(circle at 20% 80%, rgba(139, 58, 58, 0.12) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(159, 168, 139, 0.08) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(230, 181, 102, 0.06) 0%, transparent 60%);
            backdrop-filter: blur(2px);
            animation: subtleGlow 10s ease-in-out infinite;
            z-index: -1;
        }

        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(1px);
            z-index: -1;
        }

        @keyframes subtleGlow {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 0.8; }
        }

        /* Custom Cursor */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(139, 58, 58, 0.9), rgba(159, 168, 139, 0.7));
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
            box-shadow: 0 0 20px rgba(139, 58, 58, 0.5), 0 0 40px rgba(159, 168, 139, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .custom-cursor::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            width: 40px;
            height: 40px;
            border: 2px solid rgba(159, 168, 139, 0.3);
            border-radius: 50%;
            animation: cursorPulse 2s ease-in-out infinite;
        }

        @keyframes cursorPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.5; }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInFromTop {
            from {
                transform: translateY(-100px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideInFromBottom {
            from {
                transform: translateY(100px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes glow {
            0% { text-shadow: 0 0 5px rgba(139, 58, 58, 0.4); }
            50% { text-shadow: 0 0 15px rgba(184, 84, 80, 0.6), 0 0 25px rgba(159, 168, 139, 0.4); }
            100% { text-shadow: 0 0 5px rgba(139, 58, 58, 0.4); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes countdownPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 1rem;
            animation: slideInFromTop 1.5s ease-out;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .logo-container:hover {
            transform: scale(1.05);
        }

        .logo-image {
            width: 120px;
            height: 120px;
            object-fit: contain;
            margin-bottom: 1rem;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(139, 58, 58, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            animation: float 3s ease-in-out infinite;
            border: 3px solid rgba(139, 58, 58, 0.4);
            background: rgba(255, 255, 255, 0.9);
        }

        .logo-image:hover {
            box-shadow: 0 25px 60px rgba(139, 58, 58, 0.4), 0 10px 25px rgba(159, 168, 139, 0.3);
            transform: translateY(-8px);
            border-color: rgba(139, 58, 58, 0.7);
        }

        .logo-text {
            font-size: 3rem;
            font-weight: 700;
            letter-spacing: 5px;
            text-transform: uppercase;
            background: linear-gradient(135deg, #8B3A3A 0%, #B85450 50%, #9FA88B 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: glow 3s ease-in-out infinite;
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 10px rgba(0, 0, 0, 0.3));
        }

        .logo-container:hover .logo-text {
            filter: brightness(1.2);
            transform: scale(1.02);
        }

        .tagline {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 3rem;
            letter-spacing: 2px;
            max-width: 800px;
            line-height: 1.5;
            color: #2c2420;
            animation: slideInFromBottom 1.5s ease-out 0.5s both;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.9), 0 0 8px rgba(255, 255, 255, 0.6);
        }

        .tagline:hover {
            color: #8B3A3A;
            transform: translateY(-3px);
            text-shadow: 0 3px 8px rgba(139, 58, 58, 0.3);
        }

        /* Brand Owner/Seller Message Styles */
        .brand-message {
            background: rgba(255, 255, 255, 0.18);
            backdrop-filter: blur(25px);
            border-radius: 25px;
            padding: 2rem 2.5rem;
            margin: 2rem auto;
            max-width: 700px;
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 20px 45px rgba(0, 0, 0, 0.12);
            animation: slideInFromBottom 1.5s ease-out 0.8s both;
            transition: all 0.4s ease;
            text-align: center;
        }

        .brand-message:hover {
            transform: translateY(-8px) scale(1.02);
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.18);
            backdrop-filter: blur(30px);
        }

        .brand-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1a1612;
            text-transform: uppercase;
            letter-spacing: 2px;
            background: linear-gradient(135deg, #8B3A3A 0%, #B85450 50%, #9FA88B 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 8px rgba(0, 0, 0, 0.2));
            animation: glow 4s ease-in-out infinite;
        }

        .brand-description {
            font-size: 1.1rem;
            font-weight: 500;
            line-height: 1.6;
            color: #2c2420;
            margin: 0;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 0.9), 0 0 8px rgba(255, 255, 255, 0.5);
        }
        
        .countdown-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 3rem;
            animation: slideInFromBottom 1.5s ease-out 1s both;
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(25px);
            border-radius: 25px;
            padding: 2.5rem;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .countdown-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #1a1612;
            animation: float 3s ease-in-out infinite;
            transition: all 0.3s ease;
            text-shadow: 0 2px 6px rgba(255, 255, 255, 1), 0 0 12px rgba(255, 255, 255, 0.7);
        }

        .countdown-title:hover {
            color: #8B3A3A;
            transform: scale(1.05);
            text-shadow: 0 4px 12px rgba(139, 58, 58, 0.4);
        }

        .countdown {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1.5rem;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            animation: slideInFromBottom 1.5s ease-out calc(1.2s + var(--delay, 0s)) both;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(139, 58, 58, 0.1);
        }

        .countdown-item:nth-child(1) { --delay: 0s; }
        .countdown-item:nth-child(2) { --delay: 0.1s; }
        .countdown-item:nth-child(3) { --delay: 0.2s; }
        .countdown-item:nth-child(4) { --delay: 0.3s; }

        .countdown-item:hover {
            transform: translateY(-12px) scale(1.05);
            background: rgba(255, 255, 255, 0.35);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15), 0 15px 30px rgba(139, 58, 58, 0.2);
            backdrop-filter: blur(25px);
        }

        .countdown-value {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #8B3A3A 0%, #B85450 50%, #E6B566 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            line-height: 1;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .countdown-value.pulse {
            animation: countdownPulse 0.6s ease-in-out;
        }

        .countdown-label {
            font-size: 0.9rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #2c2420;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 1), 0 0 8px rgba(255, 255, 255, 0.6);
        }

        .countdown-item:hover .countdown-label {
            color: #8B3A3A;
        }
        
        .contact {
            margin-top: 2rem;
            animation: slideInFromBottom 1.5s ease-out 1.5s both;
        }

        .contact-btn {
            padding: 1.2rem 3.5rem;
            font-size: 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #fff;
            background: linear-gradient(135deg, rgba(139, 58, 58, 0.9) 0%, rgba(184, 84, 80, 0.9) 50%, rgba(159, 168, 139, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: none;
            border-radius: 50px;
            cursor: none;
            transition: all 0.4s ease;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 10px 20px rgba(139, 58, 58, 0.3);
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-block;
            border: 2px solid rgba(255, 255, 255, 0.4);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
        }

        .contact-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .contact-btn:hover::before {
            left: 100%;
        }

        .contact-btn:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25), 0 15px 30px rgba(139, 58, 58, 0.4);
            background: linear-gradient(135deg, rgba(184, 84, 80, 1) 0%, rgba(230, 181, 102, 1) 50%, rgba(159, 168, 139, 1) 100%);
            text-decoration: none;
            color: #fff;
            border-color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(25px);
            filter: brightness(1.1);
        }

        .contact-btn:active {
            transform: translateY(-2px) scale(1.02);
            transition: all 0.1s ease;
        }

        /* Sage green accent elements */
        .sage-accent {
            position: absolute;
            background: radial-gradient(circle, rgba(159, 168, 139, 0.06), transparent);
            border-radius: 50%;
            animation: sageFloat 15s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
            filter: blur(2px);
        }

        .sage-accent:nth-child(1) {
            width: 250px;
            height: 250px;
            top: 8%;
            left: 8%;
            animation-delay: 0s;
        }

        .sage-accent:nth-child(2) {
            width: 180px;
            height: 180px;
            top: 65%;
            right: 12%;
            animation-delay: 5s;
        }

        .sage-accent:nth-child(3) {
            width: 120px;
            height: 120px;
            bottom: 18%;
            left: 18%;
            animation-delay: 10s;
        }

        @keyframes sageFloat {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.2; }
            50% { transform: translateY(-25px) scale(1.05); opacity: 0.35; }
        }

        /* Loading animation for countdown */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(159, 168, 139, 0.3);
            border-radius: 50%;
            border-top-color: rgba(159, 168, 139, 0.8);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: #2c2420;
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(25px);
            font-weight: 600;
        }

        .notification.show {
            transform: translateX(0);
        }

        /* Footer Styles */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            z-index: 100;
            animation: slideInFromBottom 2s ease-out 2s both;
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
        }

        .footer-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .footer-link {
            color: #2c2420;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 700;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-shadow: 0 2px 4px rgba(255, 255, 255, 1), 0 0 8px rgba(255, 255, 255, 0.6);
        }

        .footer-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, rgba(159, 168, 139, 0.8), rgba(230, 181, 102, 0.6));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .footer-link:hover {
            color: rgba(139, 58, 58, 0.9);
            background: rgba(159, 168, 139, 0.15);
            transform: translateY(-2px);
        }

        .footer-link:hover::before {
            width: 80%;
        }

        .footer-separator {
            width: 1px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
        }

        .footer-copyright {
            color: #666;
            font-size: 0.8rem;
            font-weight: 300;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.25);
            margin: 5% auto;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
            animation: slideInFromTop 0.3s ease;
            backdrop-filter: blur(30px);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            background: linear-gradient(135deg, rgba(139, 58, 58, 0.9), rgba(159, 168, 139, 0.8));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .modal-content p {
            line-height: 1.6;
            margin-bottom: 1rem;
            color: #2c2420;
            font-weight: 400;
        }

        .close {
            color: #aaa;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close:hover {
            color: rgba(139, 58, 58, 0.9);
            background: rgba(159, 168, 139, 0.2);
            transform: rotate(90deg);
        }

        .modal-body {
            color: #ccc;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .modal-body h3 {
            color: #fff;
            margin: 1.5rem 0 1rem 0;
            font-size: 1.2rem;
        }

        .modal-body p {
            margin-bottom: 1rem;
        }

        .modal-body ul {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .modal-body li {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .logo-image {
                width: 80px;
                height: 80px;
                margin-bottom: 0.5rem;
            }

            .logo-text {
                font-size: 2rem;
            }

            .tagline {
                font-size: 1rem;
                padding: 0 1rem;
            }

            .brand-message {
                margin: 1.5rem 1rem;
                padding: 1.5rem;
            }

            .brand-title {
                font-size: 1.4rem;
                margin-bottom: 0.8rem;
            }

            .brand-description {
                font-size: 1rem;
                line-height: 1.5;
            }

            .countdown {
                gap: 1rem;
            }

            .countdown-value {
                font-size: 2.5rem;
            }

            .countdown-label {
                font-size: 0.7rem;
            }

            .contact-btn {
                padding: 0.8rem 2rem;
                font-size: 0.9rem;
            }

            .footer {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .footer-links {
                gap: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            .footer-separator {
                display: none;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 1.5rem;
            }

            .modal-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sage green accent elements -->
    <div class="sage-accent"></div>
    <div class="sage-accent"></div>
    <div class="sage-accent"></div>

    <!-- Custom cursor -->
    <div class="custom-cursor" id="customCursor"></div>

    <div class="logo-container">
        <img src="logo.png" alt="Vijino World Logo" class="logo-image">
        <div class="logo-text">Vijino World</div>
    </div>
    <!-- <div class="tagline">Time to Celebrate a Wonderful New Experience</div> -->

    <!-- Brand Owner/Seller Message -->
    <div class="brand-message">
        <h3 class="brand-title">Are You a Brand Owner or Seller?</h3>
        <p class="brand-description">
            We're opening our marketplace to passionate food & lifestyle creators like you! 🍽️ Be among the first vendors on Vijino World.
        </p>
    </div>

    <div class="countdown-container">
        <div class="countdown-title">We are launching in</div>
        <div class="countdown" id="countdown">
            <div class="countdown-item">
                <div class="countdown-value" id="days">00</div>
                <div class="countdown-label">days</div>
            </div>
            <div class="countdown-item">
                <div class="countdown-value" id="hours">00</div>
                <div class="countdown-label">hours</div>
            </div>
            <div class="countdown-item">
                <div class="countdown-value" id="minutes">00</div>
                <div class="countdown-label">minutes</div>
            </div>
            <div class="countdown-item">
                <div class="countdown-value" id="seconds">00</div>
                <div class="countdown-label">seconds</div>
            </div>
        </div>
    </div>
    
    <div class="contact">
        <a href="mailto:<EMAIL>?subject=Inquiry%20about%20Vijino%20World&body=Hello%20Vijino%20World%20Team,%0A%0AI%20am%20interested%20in%20learning%20more%20about%20your%20upcoming%20launch.%0A%0APlease%20contact%20me%20with%20more%20information.%0A%0AThank%20you!" class="contact-btn">CONTACT US</a>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-links">
            <a href="#" class="footer-link" onclick="openModal('termsModal')">Terms & Conditions</a>
            <div class="footer-separator"></div>
            <a href="#" class="footer-link" onclick="openModal('privacyModal')">Privacy Policy</a>
            <div class="footer-separator"></div>
            <span class="footer-copyright">© 2025 Vijino World. All rights reserved.</span>
        </div>
    </footer>

    <!-- Terms & Conditions Modal -->
    <div id="termsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Terms & Conditions</h2>
                <span class="close" onclick="closeModal('termsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <p><strong>Last updated:</strong> January 2024</p>

                <h3>1. Acceptance of Terms</h3>
                <p>By accessing and using Vijino World, you accept and agree to be bound by the terms and provision of this agreement.</p>

                <h3>2. Use License</h3>
                <p>Permission is granted to temporarily download one copy of Vijino World materials for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:</p>
                <ul>
                    <li>modify or copy the materials</li>
                    <li>use the materials for any commercial purpose or for any public display</li>
                    <li>attempt to reverse engineer any software contained on the website</li>
                    <li>remove any copyright or other proprietary notations from the materials</li>
                </ul>

                <h3>3. Disclaimer</h3>
                <p>The materials on Vijino World are provided on an 'as is' basis. Vijino World makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>

                <h3>4. Limitations</h3>
                <p>In no event shall Vijino World or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on Vijino World, even if Vijino World or an authorized representative has been notified orally or in writing of the possibility of such damage.</p>

                <h3>5. Contact Information</h3>
                <p>If you have any questions about these Terms & Conditions, please contact <NAME_EMAIL></p>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Privacy Policy</h2>
                <span class="close" onclick="closeModal('privacyModal')">&times;</span>
            </div>
            <div class="modal-body">
                <p><strong>Last updated:</strong> January 2024</p>

                <h3>1. Information We Collect</h3>
                <p>We collect information you provide directly to us, such as when you subscribe to our newsletter or contact us. This may include:</p>
                <ul>
                    <li>Email addresses</li>
                    <li>Name and contact information</li>
                    <li>Any other information you choose to provide</li>
                </ul>

                <h3>2. How We Use Your Information</h3>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Send you updates about Vijino World</li>
                    <li>Respond to your comments and questions</li>
                    <li>Improve our services</li>
                    <li>Comply with legal obligations</li>
                </ul>

                <h3>3. Information Sharing</h3>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy or as required by law.</p>

                <h3>4. Data Security</h3>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

                <h3>5. Your Rights</h3>
                <p>You have the right to:</p>
                <ul>
                    <li>Access your personal information</li>
                    <li>Correct inaccurate information</li>
                    <li>Request deletion of your information</li>
                    <li>Unsubscribe from our communications</li>
                </ul>

                <h3>6. Contact Us</h3>
                <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL></p>
            </div>
        </div>
    </div>

    <script>
        // Set the date we're counting down to (14 days from now)
        const countDownDate = new Date();
        countDownDate.setDate(countDownDate.getDate() + 14);

        let previousValues = { days: null, hours: null, minutes: null, seconds: null };

        // Function to add pulse animation when value changes
        function addPulseAnimation(elementId) {
            const element = document.getElementById(elementId);
            element.classList.add('pulse');
            setTimeout(() => {
                element.classList.remove('pulse');
            }, 600);
        }

        // Function to show notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Update the countdown every 1 second
        const x = setInterval(function() {
            // Get today's date and time
            const now = new Date().getTime();

            // Find the distance between now and the countdown date
            const distance = countDownDate - now;

            // Time calculations for days, hours, minutes and seconds
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            // Check for changes and add pulse animation
            if (previousValues.days !== null && previousValues.days !== days) {
                addPulseAnimation('days');
            }
            if (previousValues.hours !== null && previousValues.hours !== hours) {
                addPulseAnimation('hours');
            }
            if (previousValues.minutes !== null && previousValues.minutes !== minutes) {
                addPulseAnimation('minutes');
            }
            if (previousValues.seconds !== null && previousValues.seconds !== seconds) {
                addPulseAnimation('seconds');
            }

            // Update previous values
            previousValues = { days, hours, minutes, seconds };

            // Display the result with smooth transitions
            document.getElementById("days").innerHTML = days.toString().padStart(2, '0');
            document.getElementById("hours").innerHTML = hours.toString().padStart(2, '0');
            document.getElementById("minutes").innerHTML = minutes.toString().padStart(2, '0');
            document.getElementById("seconds").innerHTML = seconds.toString().padStart(2, '0');

            // If the countdown is finished, write some text
            if (distance < 0) {
                clearInterval(x);
                document.getElementById("countdown").innerHTML = "<div style='font-size: 3rem; color: #6e45e2; animation: pulse 1s infinite;'>WE'RE LIVE!</div>";
                showNotification("🎉 Vijino World is now live!");
            }
        }, 1000);
        
        // Custom cursor functionality
        const cursor = document.getElementById('customCursor');
        let mouseX = 0;
        let mouseY = 0;
        let cursorX = 0;
        let cursorY = 0;

        // Track mouse movement
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        // Smooth cursor animation
        function animateCursor() {
            const speed = 0.15;
            cursorX += (mouseX - cursorX) * speed;
            cursorY += (mouseY - cursorY) * speed;

            cursor.style.left = cursorX - 10 + 'px';
            cursor.style.top = cursorY - 10 + 'px';

            requestAnimationFrame(animateCursor);
        }

        animateCursor();

        // Cursor hover effects
        const hoverElements = document.querySelectorAll('.contact-btn, .footer-link, .logo-container, .countdown-item');

        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                cursor.style.transform = 'scale(1.4)';
                cursor.style.background = 'radial-gradient(circle, rgba(139, 58, 58, 1), rgba(159, 168, 139, 0.9))';
                cursor.style.boxShadow = '0 0 30px rgba(139, 58, 58, 0.6), 0 0 50px rgba(159, 168, 139, 0.4)';
                cursor.style.borderColor = 'rgba(255, 255, 255, 1)';
            });

            element.addEventListener('mouseleave', () => {
                cursor.style.transform = 'scale(1)';
                cursor.style.background = 'radial-gradient(circle, rgba(139, 58, 58, 0.9), rgba(159, 168, 139, 0.7))';
                cursor.style.boxShadow = '0 0 20px rgba(139, 58, 58, 0.5), 0 0 40px rgba(159, 168, 139, 0.3)';
                cursor.style.borderColor = 'rgba(255, 255, 255, 0.8)';
            });
        });
        
        // Enhanced contact button functionality
        document.querySelector('.contact-btn').addEventListener('click', function(e) {
            // Add visual feedback when clicked
            const button = this;
            const originalTransform = button.style.transform;

            // Add click animation
            button.style.transform = 'translateY(-2px) scale(1.02)';
            button.style.boxShadow = '0 25px 50px rgba(159, 168, 139, 0.6)';

            // Show notification
            showNotification('📧 Opening your email client...');

            // Reset animation after a short delay
            setTimeout(() => {
                button.style.transform = originalTransform;
                button.style.boxShadow = '0 10px 20px rgba(139, 58, 58, 0.2)';
            }, 200);
        });

        // Add click effects to logo
        document.querySelector('.logo-container').addEventListener('click', function() {
            const logoImage = this.querySelector('.logo-image');
            const logoText = this.querySelector('.logo-text');

            // Reset animations
            this.style.animation = 'none';
            logoImage.style.animation = 'none';
            logoText.style.animation = 'none';

            setTimeout(() => {
                this.style.animation = 'slideInFromTop 1.5s ease-out';
                logoImage.style.animation = 'float 3s ease-in-out infinite';
                logoText.style.animation = 'glow 3s ease-in-out infinite';
            }, 10);

            showNotification('✨ Welcome to Vijino World!');
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                document.querySelector('.contact-btn').click();
            }
        });

        // Add sage green sparkle effects on click
        document.addEventListener('click', function(e) {
            for (let i = 0; i < 5; i++) {
                const sparkle = document.createElement('div');
                sparkle.style.position = 'fixed';
                sparkle.style.left = (e.clientX + Math.random() * 40 - 20) + 'px';
                sparkle.style.top = (e.clientY + Math.random() * 40 - 20) + 'px';
                sparkle.style.width = '6px';
                sparkle.style.height = '6px';
                sparkle.style.background = 'radial-gradient(circle, rgba(139, 58, 58, 0.8), rgba(159, 168, 139, 0.6))';
                sparkle.style.borderRadius = '50%';
                sparkle.style.pointerEvents = 'none';
                sparkle.style.zIndex = '999';
                sparkle.style.animation = 'sparkleOut 0.8s ease-out forwards';
                document.body.appendChild(sparkle);

                setTimeout(() => {
                    if (sparkle.parentNode) {
                        sparkle.parentNode.removeChild(sparkle);
                    }
                }, 800);
            }
        });

        // Add sparkle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkleOut {
                0% { opacity: 1; transform: scale(1) translateY(0); }
                100% { opacity: 0; transform: scale(0.3) translateY(-30px); }
            }
        `;
        document.head.appendChild(style);

        // Modal functionality
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';

            // Add click outside to close
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal(modalId);
                }
            });
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
            }
        });

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>